# HRUN项目Docker部署指南

## 系统要求

- macOS 10.14 或更高版本
- Docker Desktop for Mac
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

## 快速部署

### 1. 安装Docker Desktop

如果还没有安装Docker Desktop，请从官网下载安装：
```
https://www.docker.com/products/docker-desktop
```

安装完成后启动Docker Desktop，确保Docker正在运行。

### 2. 一键部署

在项目根目录执行：
```bash
./deploy-docker.sh
```

该脚本会自动：
- 检查Docker环境
- 停止现有容器
- 构建并启动所有服务
- 检查服务健康状态
- 显示访问地址

### 3. 访问应用

部署成功后，可以通过以下地址访问：

- **前端页面**: http://localhost:81
- **后端API**: http://localhost:8000
- **后端管理**: http://localhost:8000/admin/
- **数据库**: localhost:3306

### 4. 默认账号

- **管理员用户名**: admin
- **管理员密码**: 123456

## 手动部署

如果需要手动控制部署过程：

### 1. 构建并启动服务
```bash
docker-compose up --build -d
```

### 2. 查看服务状态
```bash
docker-compose ps
```

### 3. 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f nginx
docker-compose logs -f mariadb
```

## 服务架构

项目包含以下服务：

1. **mariadb**: 数据库服务 (端口: 3306)
2. **redis**: 缓存服务
3. **backend**: Django后端服务 (端口: 8000)
4. **nginx**: 前端和反向代理服务 (端口: 80, 81)

## 常用命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart backend

# 重新构建并启动
docker-compose up --build -d
```

### 数据管理
```bash
# 备份数据库
docker-compose exec mariadb mysqldump -u root -ppythonvip HRUN > backup.sql

# 恢复数据库
docker-compose exec -T mariadb mysql -u root -ppythonvip HRUN < backup.sql

# 清理所有数据（谨慎使用）
docker-compose down -v
```

### 调试和维护
```bash
# 进入后端容器
docker-compose exec backend bash

# 进入数据库容器
docker-compose exec mariadb bash

# 查看容器资源使用情况
docker stats

# 清理未使用的Docker资源
docker system prune -f
```

## 环境配置

项目使用 `.env` 文件进行环境配置，主要配置项：

```env
# 数据库配置
DB_HOST=mariadb
DB_PORT=3306
DB_NAME=HRUN
DB_USER=hrun
DB_PASSWORD=hrun123

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379

# Django配置
ENV=production
TZ=Asia/Shanghai
```

## 故障排除

### 1. 端口冲突
如果遇到端口冲突，可以修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8080:8000"  # 将8000改为8080
```

### 2. 数据库连接失败
检查数据库是否正常启动：
```bash
docker-compose logs mariadb
```

### 3. 前端页面无法访问
检查nginx服务状态：
```bash
docker-compose logs nginx
```

### 4. 内存不足
如果系统内存不足，可以限制容器内存使用：
```yaml
deploy:
  resources:
    limits:
      memory: 512M
```

## 性能优化

### 1. 数据卷优化
对于macOS系统，建议使用命名卷而不是绑定挂载以提高性能。

### 2. 资源限制
根据系统配置调整容器资源限制：
```yaml
deploy:
  resources:
    limits:
      cpus: '0.5'
      memory: 512M
```

## 安全建议

1. 修改默认密码
2. 使用强密码
3. 限制网络访问
4. 定期更新镜像
5. 监控日志

## 更新部署

更新代码后重新部署：
```bash
git pull
./deploy-docker.sh
```

或者手动更新：
```bash
docker-compose down
docker-compose up --build -d
```
