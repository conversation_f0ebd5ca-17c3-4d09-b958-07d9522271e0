#!/bin/bash

# HRUN项目Docker部署脚本
# 适用于macOS系统

set -e

echo "🚀 开始部署HRUN项目..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker Desktop for Mac"
    echo "下载地址: https://www.docker.com/products/docker-desktop"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker未运行，请启动Docker Desktop"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装，请安装docker-compose"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 停止并删除现有容器
echo "🛑 停止现有容器..."
docker-compose down -v 2>/dev/null || true

# 清理旧的镜像（可选）
read -p "是否清理旧的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
    docker image prune -f
fi

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 检查服务健康状态
echo "🏥 检查服务健康状态..."

# 检查数据库连接
echo "检查数据库连接..."
if docker-compose exec -T mariadb mysql -u root -ppythonvip -e "SELECT 1;" &> /dev/null; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
fi

# 检查Redis连接
echo "检查Redis连接..."
if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
fi

# 检查后端服务
echo "检查后端服务..."
sleep 10
if curl -f http://localhost:8000/admin/ &> /dev/null; then
    echo "✅ 后端服务正常"
else
    echo "⚠️  后端服务可能还在启动中，请稍后检查"
fi

# 检查前端服务
echo "检查前端服务..."
if curl -f http://localhost:81/ &> /dev/null; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务访问地址："
echo "   前端页面: http://localhost:81"
echo "   后端API: http://localhost:8000"
echo "   后端管理: http://localhost:8000/admin/"
echo "   数据库: localhost:3306"
echo ""
echo "🔑 默认管理员账号："
echo "   用户名: admin"
echo "   密码: 123456"
echo ""
echo "📝 常用命令："
echo "   查看日志: docker-compose logs -f [服务名]"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart [服务名]"
echo "   进入容器: docker-compose exec [服务名] bash"
echo ""
echo "🔍 如果遇到问题，请检查日志："
echo "   docker-compose logs backend"
echo "   docker-compose logs nginx"
echo "   docker-compose logs mariadb"
