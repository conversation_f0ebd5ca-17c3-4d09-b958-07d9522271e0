# 指定版本号
version: "3.8"

# 服务，表示要启动的容器服务
services:
  # Redis服务
  redis:
    image: redis:alpine
    restart: always
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - hrun_network

  # MariaDB数据库服务
  mariadb:
    image: mariadb:10.5
    restart: always
    environment:
      MARIADB_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-pythonvip}
      MARIADB_DATABASE: ${DB_NAME:-HRUN}
      MARIADB_USER: ${DB_USER:-hrun}
      MARIADB_PASSWORD: ${DB_PASSWORD:-hrun123}
      TZ: ${TZ:-Asia/Shanghai}
    volumes:
      - mariadb_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - hrun_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Django后端服务
  backend:
    depends_on:
      - redis
      - mariadb
    build: ./backend
    image: backend_django_image
    environment:
      ENV: production
      DB_HOST: mariadb
      DB_PORT: 3306
      DB_NAME: HRUN
      DB_USER: hrun
      DB_PASSWORD: hrun123
      REDIS_HOST: redis
      REDIS_PORT: 6379
    restart: always
    volumes:
      - app_logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - hrun_network

  # Nginx前端服务
  nginx:
    depends_on:
      - backend
    build: ./nginx
    image: backend_nginx_image
    ports:
      - "80:80"
      - "81:81"
    volumes:
      - nginx_logs:/var/log/nginx
    networks:
      - hrun_network

# 数据卷定义
volumes:
  mariadb_data:
  redis_data:
  app_logs:
  nginx_logs:

# 网络定义
networks:
  hrun_network:
    driver: bridge